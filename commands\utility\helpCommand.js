module.exports = {
  command: "help",
  aliases: ["commands", "h"],
  category: "utility",
  description: "Menampilkan daftar command yang tersedia",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const excludedCategories = ["nonprefix"];
    let name;
    if(event.source.type === "group") {
      name = await client.getGroupSummary(event.source.groupId)
    }
    else {
      name = "kamu"
    }
    const howTo = 
      `- Seluruh command dibawah ini diaktifkan menggunakan "!"\n`+
      `- Untuk melihat command di kategori tertentu, ketik "!help <kategori>"\n`+
      `- Untuk melihat detail dari command, ketik "!help <command>"\n`+
      `- Untuk melihat daftar command ini lagi, ketik "!help"`
    
    const categoryEmojis = {
      anime: "🎴",
      utility: "🛠️",
      fun: "🎉",
      misc: "*️⃣",
    };

    const fieldIcons = {
      command: "🔹",
      description: "📝",
      aliases: "🔀",
      category: "📂",
    };

    // Convert Map to array and filter
    const filteredCommands = Array.from(client.commands.values()).filter(
      (cmd) => !excludedCategories.includes(cmd.category)
    );

    // Get unique categories
    const categories = [...new Set(filteredCommands.map((x) => x.category))];

    if (!args[0]) {
      const categoryList = categories
        .map((category) => {
          const commandsInCategory = filteredCommands
            .filter((x) => x.category === category)
            .map((x) => `[${x.command}]`)
            .join(", ");
          return `${categoryEmojis[category] || ""} ${
            category.charAt(0).toUpperCase() + category.slice(1)
          } Commands\n${commandsInCategory}`;
        })
        .join("\n\n");

      client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: `Daftar command yang tersedia untuk ${name.groupName ? name.groupName : "chat ini"} :\n\n${howTo}\n\n${categoryList}`,
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    } else {
      const categoryRequested = args[0].toLowerCase();
      if (categories.includes(categoryRequested)) {
        const commandsInCategory = filteredCommands
          .filter((x) => x.category === categoryRequested)
          .map((x) => `[${x.command}]`)
          .join(", ");

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `Command dalam kategori ${
                categoryEmojis[categoryRequested] || ""
              } ${categoryRequested}:\n${commandsInCategory}`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      } else {
        // Find in array instead of Enmap
        const command = filteredCommands.find((x) => x.command === args[0]) ||
          filteredCommands.find(
            (x) => x.aliases && x.aliases.includes(args.join(" ").toLowerCase())
          );

        if (!command) {
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Command atau kategori tidak ditemukan.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: `${fieldIcons.command} Command: ${command.command}\n${
                fieldIcons.description
              } Description: ${command.description}\n${
                fieldIcons.aliases
              } Aliases: ${command.aliases.join(", ")}\n${
                fieldIcons.category
              } Category: ${
                command.category.charAt(0).toUpperCase() + command.category.slice(1)
              }`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    }
  },
};