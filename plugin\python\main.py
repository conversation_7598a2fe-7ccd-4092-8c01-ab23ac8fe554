from flask import Flask, jsonify, request
from g4f.client import Client
from g4f.Provider import Copilot
import requests
from google import genai 
import os
from dotenv import load_dotenv
app = Flask(__name__)
# Load environment variables from .env file
load_dotenv()
# Initialize your GenAI client once
genai_client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        messages = data.get("messages")

        if not messages:
            return jsonify({"error": "Messages are required"}), 400

        client = Client(provider=Copilot, shuffle=False)
        response = client.chat.completions.create(
            model="Copilot",
            messages=messages
        )
        content = response.choices[0].message.content
        return content
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/image', methods=['POST'])
def generate_image():
    try:
        data = request.get_json()
        prompt = data.get("prompt")

        if not prompt:
            return jsonify({"error": "Prompt is required"}), 400

        client = Client()
        response = client.images.generate(
            model="flux",
            prompt=prompt,
            response_format="url"
        )
        image_url = response.data[0].url
        return jsonify({"url": image_url})
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/analyze_image', methods=['POST'])
def analyze_image():
    try:
        data = request.get_json()
        image_url = data.get("image_url")
        question = data.get("question", "What is in this image?")

        if not image_url:
            return jsonify({"error": "Image URL is required"}), 400

        # Download the image locally
        try:
            image_response = requests.get(image_url, stream=True)
            image_response.raise_for_status()
            with open("temp_image.png", "wb") as f:
                for chunk in image_response.iter_content(1024):
                    f.write(chunk)
        except requests.exceptions.RequestException as e:
            return jsonify({"error": f"Error fetching image: {str(e)}"}), 400

        # Upload to genai
        uploaded_file = genai_client.files.upload(file="temp_image.png")

        # Run the image analysis
        response = genai_client.models.generate_content(
            model="gemini-2.0-flash",
            contents=[uploaded_file, question]
        )

        return jsonify({"answer": response.text})

    except Exception as e:
        return jsonify({"error": str(e)}), 500

    finally:
        # Clean up the temp image file if it exists
        if os.path.exists("temp_image.png"):
            os.remove("temp_image.png")


if __name__ == "__main__":
    app.run(port=5000)
