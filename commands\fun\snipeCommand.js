const { QuickDB } = require("quick.db");
const db = new QuickDB();

module.exports = {
  command: "snipe",
  aliases: [],
  category: "fun",
  description: "Show deleted messages from mentioned user (!snipe @user)",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // Only works in groups
    if (event.source.type !== "group") {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Snipe command only works in group chats!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Check if user mentioned someone
    if (!event.message.mention || !event.message.mention.mentionees || event.message.mention.mentionees.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Please mention a user to snipe their deleted messages!\nUsage: !snipe @username",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const targetUserId = event.message.mention.mentionees[0].userId;
    const groupId = event.source.groupId;
    
    // Get user's messages
    const userMessagesKey = `user_messages_${groupId}_${targetUserId}`;
    const userMessageIds = await db.get(userMessagesKey) || [];
    
    if (userMessageIds.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ No messages found for this user!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Find deleted messages within the last 2 hours
    const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);
    const deletedMessages = [];

    for (const messageId of userMessageIds) {
      const messageData = await db.get(`message_${groupId}_${messageId}`);
      if (messageData && messageData.deleted && messageData.deletedAt > twoHoursAgo) {
        deletedMessages.push(messageData);
      }
    }

    if (deletedMessages.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ No deleted messages found within the last 2 hours!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Sort by deletion time (most recent first) and take up to 5
    deletedMessages.sort((a, b) => b.deletedAt - a.deletedAt);
    const messagesToShow = deletedMessages.slice(0, 5);

    // Get target user's profile for display name
    let targetUserName = "Unknown User";
    try {
      const profile = await client.getGroupMemberProfile(groupId, targetUserId);
      targetUserName = profile.displayName;
    } catch (error) {
      console.error("Error getting user profile:", error);
    }

    // Format the messages
    let responseText = `🎯 Sniped messages from ${targetUserName}:\n\n`;
    
    messagesToShow.forEach((msg, index) => {
      const deletedTime = new Date(msg.deletedAt);
      const timeStr = deletedTime.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit' 
      });
      
      responseText += `${index + 1}. [${timeStr}] "${msg.text}"\n`;
    });

    responseText += `\n📝 Showing ${messagesToShow.length} of ${deletedMessages.length} deleted messages`;

    return client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: responseText,
          quoteToken: event.message.quoteToken,
        },
      ],
    });
  },
};
