const { QuickDB } = require("quick.db");
const db = new QuickDB();

module.exports = {
  command: "snipe",
  aliases: [],
  category: "fun",
  description: "Show deleted messages from mentioned user (!snipe @user)",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    // Only works in groups
    if (event.source.type !== "group") {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Snipe command only works in group chats!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Check if user mentioned someone
    if (!event.message.mention || !event.message.mention.mentionees || event.message.mention.mentionees.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ Please mention a user to snipe their deleted messages!\nUsage: !snipe @username",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const targetUserId = event.message.mention.mentionees[0].userId;
    const groupId = event.source.groupId;
    
    // Get user's messages
    const userMessagesKey = `user_messages_${groupId}_${targetUserId}`;
    const userMessageIds = await db.get(userMessagesKey) || [];
    
    if (userMessageIds.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ No messages found for this user!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Find deleted messages within the last 2 hours
    const twoHoursAgo = Date.now() - (2 * 60 * 60 * 1000);
    const deletedMessages = [];

    for (const messageId of userMessageIds) {
      const messageData = await db.get(`message_${groupId}_${messageId}`);
      if (messageData && messageData.deleted && messageData.deletedAt > twoHoursAgo) {
        deletedMessages.push(messageData);
      }
    }

    if (deletedMessages.length === 0) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "❌ No deleted messages found within the last 2 hours!",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // Sort by deletion time (most recent first) and take up to 5
    deletedMessages.sort((a, b) => b.deletedAt - a.deletedAt);
    const messagesToShow = deletedMessages.slice(0, 5);

    // Get target user's profile for display name
    let targetUserName = "Unknown User";
    try {
      const profile = await client.getGroupMemberProfile(groupId, targetUserId);
      targetUserName = profile.displayName;
    } catch (error) {
      console.error("Error getting user profile:", error);
    }

    // Format the messages with character limit handling
    const messages = [];
    let currentMessage = `🎯 Sniped messages from ${targetUserName}:\n\n`;
    const footer = `\n📝 Showing ${messagesToShow.length} of ${deletedMessages.length} deleted messages`;
    const maxLength = 2000;
    let truncated = false;

    for (let index = 0; index < messagesToShow.length; index++) {
      const msg = messagesToShow[index];
      const deletedTime = new Date(msg.deletedAt);
      const timeStr = deletedTime.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });

      const messageEntry = `${index + 1}. [${timeStr}] "${msg.text}"\n`;

      // Check if adding this message would exceed the limit
      const potentialLength = currentMessage.length + messageEntry.length + footer.length;

      if (potentialLength > maxLength && currentMessage.trim() !== `🎯 Sniped messages from ${targetUserName}:`) {
        // Add current message to array and start a new one
        messages.push({
          type: "text",
          text: currentMessage.trim(),
          quoteToken: event.message.quoteToken,
        });

        // Start new message (only if we haven't reached 5 message limit)
        if (messages.length < 4) { // Keep space for footer message
          currentMessage = `🎯 Continued...\n\n${messageEntry}`;
        } else {
          // If we're at the limit, truncate and add footer to last message
          const lastMessage = messages[messages.length - 1];
          lastMessage.text += `\n\n⚠️ More messages truncated due to length limit`;
          truncated = true;
          break;
        }
      } else {
        currentMessage += messageEntry;
      }
    }

    // Add the final message with footer
    if (currentMessage.trim() !== `🎯 Sniped messages from ${targetUserName}:`) {
      if (!truncated) {
        currentMessage += footer;
      }
      messages.push({
        type: "text",
        text: currentMessage,
        quoteToken: event.message.quoteToken,
      });
    }

    // Ensure we don't exceed 5 messages
    const finalMessages = messages.slice(0, 5);

    return client.replyMessage({
      replyToken: event.replyToken,
      messages: finalMessages,
    });
  },
};
