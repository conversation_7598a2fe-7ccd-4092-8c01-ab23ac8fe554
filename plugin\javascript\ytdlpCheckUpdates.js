const { spawn } = require("child_process");
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const winston = require("winston");


// Logger untuk output
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
    winston.format.printf(
      ({ level, message, timestamp }) => `[${timestamp}] ${level}: ${message}`
    )
  ),
  transports: [new winston.transports.Console()],
});

const ytDlpPath = path.join(__dirname, "../../bin/yt-dlp.exe"); // Path ke yt-dlp lokal
const ytDlpUrl = "https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe"; // URL download

// Fungsi untuk mendapatkan versi yt-dlp lokal
async function getLocalVersion() {
  return new Promise((resolve, reject) => {
    const ytDlp = spawn(ytDlpPath, ["--version"], {
      windowsHide: true,
    });

    let version = "";
    ytDlp.stdout.on("data", (data) => {
      version += data.toString();
    });

    ytDlp.stderr.on("data", (data) => {
      logger.error(`yt-dlp stderr: ${data.toString().trim()}`);
    });

    ytDlp.on("close", (code) => {
      if (code === 0) {
        resolve(version.trim());
      } else {
        reject(new Error(`Failed to get yt-dlp version. Exit code: ${code}`));
      }
    });
  });
}

// Fungsi untuk mendapatkan versi terbaru dari GitHub
async function getLatestVersion() {
  try {
    const response = await axios.get("https://api.github.com/repos/yt-dlp/yt-dlp/releases/latest");
    return response.data.tag_name; // Format versi seperti "2023.03.04"
  } catch (error) {
    logger.error("Error fetching latest yt-dlp version:", error.message);
    throw error;
  }
}

// Fungsi untuk mengupdate yt-dlp jika diperlukan
async function updateYtDlp() {
  try {
    const localVersion = await getLocalVersion();
    const latestVersion = await getLatestVersion();

    logger.info(`Local yt-dlp version: ${localVersion}`);
    logger.info(`Latest yt-dlp version: ${latestVersion}`);

    if (localVersion === latestVersion) {
      logger.info("yt-dlp is already up-to-date!");
      return;
    }

    logger.info("Updating yt-dlp...");
    const tempFilePath = path.join(__dirname, "../../bin/yt-dlp.temp.exe");
    const curl = spawn("curl", ["-L", ytDlpUrl, "-o", tempFilePath], {
      windowsHide: true,
    });

    curl.on("close", (code) => {
      if (code === 0) {
        fs.unlink(ytDlpPath, (err) => {
          if (err) {
            logger.error("Error removing old yt-dlp file:", err);
            return;
          }
          fs.rename(tempFilePath, ytDlpPath, (err) => {
            if (err) {
              logger.error("Error renaming new yt-dlp file:", err);
              return;
            }
            logger.info("yt-dlp updated successfully!");
          });
        });
      } else {
        logger.error(`Failed to download yt-dlp. curl exit code: ${code}`);
      }
    });
  } catch (error) {
    logger.error("Error during yt-dlp update process:", error.message);
  }
}

module.exports = {
  updateYtDlp,
};