module.exports = async (event, client, blobClient) => {
  const groupId = event.source.groupId;

  // Loop through each user who left the group
  const usernames = await Promise.all(
    event.left.members.map(async (member) => {
      try {
        const userProfile = await client.getGroupMemberProfile(
          groupId,
          member.userId
        );
        return userProfile.displayName;
      } catch (error) {
        console.error(
          `Failed to get user profile for user ID ${member.userId}:`,
          error
        );
        return "Seseorang"; // Default name if the profile fetch fails
      }
    })
  );

  // Join all usernames or use "Seseorang" as fallback
  const messageText = `${usernames.join(
    ", "
  )} telah meninggalkan grup, selamat tinggal! Semoga bisa bertemu lagi`;

  // Send the goodbye message to the group
  await client.replyMessage({
    replyToken: event.replyToken,
    messages: [
      {
        type: "text",
        text: messageText,
      },
    ],
  });
};
