const axios = require("axios");
const { QuickDB } = require("quick.db");
const db = new QuickDB();

const HOLODEX_API_KEY = process.env.HOLODEX_API_KEY;
const channelIds = ["UCrV1Hf5r8P148idjoSfrGEQ", "UC1DCedRgGHBdm81E1llLhOQ"]; // Ganti dengan ID channel VTuber
// Ganti dengan ID channel VTuber

// Fungsi untuk menunda eksekusi
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Fungsi untuk mendapatkan nama channel dari Holodex
async function getChannelName(channelId) {
  try {
    const response = await axios.get(
      `https://holodex.net/api/v2/channels/${channelId}`,
      {
        headers: {
          "X-APIKEY": HOLODEX_API_KEY,
        },
      }
    );

    return response.data.name;
  } catch (error) {
    console.error("Gagal mendapatkan nama channel:", error.statusMessage);
    return null;
  }
}

// Fungsi untuk mendapatkan video terbaru dan status live dari Holodex API
async function checkYouTubeStatus(client) {
  try {
    for (const channelId of channelIds) {
      const channelName = await getChannelName(channelId);
      if (!channelName) continue;

      // Penundaan antara permintaan untuk menghindari terlalu banyak permintaan
      await delay(1000);

      try {
        // Dapatkan video terbaru dari channel menggunakan Holodex
        const videoResponse = await axios.get(
          `https://holodex.net/api/v2/channels/${channelId}/videos`,
          {
            headers: {
              "X-APIKEY": HOLODEX_API_KEY,
            },
            params: {
              limit: 1,
              type: "videos",
              include: "clips",
              order: "newest",
            },
          }
        );

        // Penundaan antara permintaan untuk menghindari terlalu banyak permintaan
        await delay(1000);

        // Dapatkan informasi apakah channel sedang live atau upcoming
        const liveResponse = await axios.get(`https://holodex.net/api/v2/live`, {
          headers: {
            "X-APIKEY": HOLODEX_API_KEY,
          },
          params: {
            channel_id: channelId,
            status: "live,upcoming",
          },
        });

        const latestVideo = videoResponse.data[0];
        const liveVideos = liveResponse.data.filter(video => video.status === 'live');
        const upcomingVideos = liveResponse.data.filter(video => video.status === 'upcoming');
        const isLive = liveVideos.length > 0;
        const isUpcoming = upcomingVideos.length > 0;
        const liveVideo = liveVideos[0];
        const upcomingVideo = upcomingVideos[0];

        // Ambil status notifikasi terakhir dari database
        const lastNotifiedVideoId = await db.get(`${channelId}_lastNotifiedVideoId`);
        const wasLiveNotified = await db.get(`${channelId}_wasLiveNotified`);
        const wasUpcomingNotified = await db.get(`${channelId}_wasUpcomingNotified`);

        // Jika ada video terbaru yang belum diberi notifikasi
        if (latestVideo && latestVideo.id !== lastNotifiedVideoId) {
          const messageText = `${channelName} baru saja mengupload video: ${latestVideo.title}.\nLink: https://www.youtube.com/watch?v=${latestVideo.id}`;

          // Kirim notifikasi untuk video baru
          await client.pushMessage({
            to: process.env.sirius,
            messages: [
              {
                type: "text",
                text: messageText,
              },
            ],
          });

          // Perbarui status video yang sudah diberi notifikasi
          await db.set(`${channelId}_lastNotifiedVideoId`, latestVideo.id);
          console.log("Notifikasi video baru berhasil dikirim!");
        }

        // Jika VTuber baru saja live dan belum ada notifikasi live
        if (isLive && !wasLiveNotified) {
          const liveMessageText = `${channelName} saat ini sedang live!\nLink live: https://www.youtube.com/watch?v=${liveVideo.id}`;

          // Kirim notifikasi untuk live streaming
          await client.pushMessage({
            to: process.env.sirius,
            messages: [
              {
                type: "text",
                text: liveMessageText,
              },
            ],
          });

          // Perbarui status live yang sudah diberi notifikasi
          await db.set(`${channelId}_wasLiveNotified`, true);
          console.log("Notifikasi live streaming berhasil dikirim!");
        }
        // Reset status live ketika sudah tidak live lagi
        else if (!isLive && wasLiveNotified) {
          await db.set(`${channelId}_wasLiveNotified`, false);
        }

        // Jika ada upcoming live dan belum ada notifikasi
        if (isUpcoming && !wasUpcomingNotified) {
          const upcomingMessageText = `${channelName} akan live segera!\nLink upcoming live: https://www.youtube.com/watch?v=${upcomingVideo.id}`;

          // Kirim notifikasi untuk upcoming live
          await client.pushMessage({
            to: process.env.sirius,
            messages: [
              {
                type: "text",
                text: upcomingMessageText,
              },
            ],
          });

          // Perbarui status upcoming live yang sudah diberi notifikasi
          await db.set(`${channelId}_wasUpcomingNotified`, true);
          console.log("Notifikasi upcoming live berhasil dikirim!");
        }
        // Reset status upcoming live ketika sudah live atau sudah tidak upcoming
        else if (!isUpcoming && wasUpcomingNotified) {
          await db.set(`${channelId}_wasUpcomingNotified`, false);
        }

      } catch (error) {
        if (error.response && error.response.status === 429) {
          console.error(`Terlalu banyak permintaan ke Holodex API untuk channel ${channelId}. Coba lagi nanti.`);
        } else {
          console.error(`Gagal mendapatkan status YouTube untuk channel ${channelId}:`, error.status);
          // delete database jika gagal mendapatkan status
          await db.delete(`${channelId}_lastNotifiedVideoId`);
        }
      }
    }
  } catch (error) {
    console.error("Gagal mendapatkan status YouTube dari Holodex API:", error.statusMessage);
  }
}

// Ekspor fungsi untuk digunakan di file lain
module.exports = { checkYouTubeStatus };
