{"Command Handler Template": {"prefix": "command", "body": ["module.exports = {", "  command: \"${1:commandName}\",", "  aliases: [\"${2:alias}\"],", "  category: \"${3:category}\",", "  description: \"${4:description}\",", "  requiresPrefix: ${5:true}, // Set to false to enable prefix-less activation", "  includes: ${6:false}, // Set to true if the command should trigger when included in a message", "  handler: async (client, blobClient, event, args) => {", "    $7", "  }", "};"], "description": "Template for a command handler module"}, "client.replyMessage Template": {"prefix": "replyMessage", "body": ["client.replyMessage({", "  replyToken: event.replyToken,", "  messages: [", "    {", "      type: \"${1:text}\",", "      text: \"${2:Your message here}\"", "    }", "  ]", "});"], "description": "Template for client.replyMessage structure"}, "Postback Handler Template": {"prefix": "postback", "body": ["module.exports = {", "  postbackData: \"${1:postbackData}\", // Prefix for dynamic sticker postbacks", "  cooldown: ${2:0},", "  handler: async (client, blobClient, event, id) => {", "    $3", "  }", "};"], "description": "Template for postback handler module"}}