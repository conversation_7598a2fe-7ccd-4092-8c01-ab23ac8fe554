const { format, formatDistanceToNow } = require('date-fns');
const { id } = require('date-fns/locale');

 // Formatting functions
 const formatUploadDate = (date) => 
  format(date, "EEEE do MMMM yyyy", { locale: id });

const formatDuration = (date) => 
  formatDistanceToNow(date, { 
    addSuffix: false,
    locale: id,
    includeSeconds: false
  });

// Workaround imports

// const { CookieJar } = require('tough-cookie');
// const _httpCookie = require('http-cookie-agent/http');
// const { HttpsCookieAgent: CookieAgent } = _httpCookie

module.exports = {
  postbackData: "nhentaiDetailsData=", 
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {

    const { API, TagTypes } = require("nhentai-api");

    const api = new API();
    const book = await api.getBook(id)
    const tags = book.getTagsWith({ type: TagTypes.Tag }).join(", ");
    const artists = book
      .getTagsWith({ type: TagTypes.Artist })
      .join(", ");
    const languages = book
      .getTagsWith({ type: TagTypes.Language })
      .join(", ");
    const categories = book
      .getTagsWith({ type: TagTypes.Category })
      .join(", ");
    const parodies = book
      .getTagsWith({ type: TagTypes.Parody })
      .join(", ");
    const characters = book
      .getTagsWith({ type: TagTypes.Character })
      .join(", ");
    const groups = book.getTagsWith({ type: TagTypes.Group }).join(", ");
    console.log(book);

    const replyText =
      (book.title.pretty ? `Title : ${book.title.pretty}\n` : "") +
      (book.title.english
        ? `English Title : ${book.title.english}\n`
        : "") +
      (book.title.japanese
        ? `Japanese Title : ${book.title.japanese}\n`
        : "") +
      (tags ? `\nTags : ${tags}\n\n` : "") +
      (artists ? `Artists : ${artists}\n` : "") +
      (languages ? `Languages : ${languages}\n` : "") +
      (categories ? `Categories : ${categories}\n` : "") +
      (parodies ? `Parodies : ${parodies}\n` : "") +
      (characters ? `Characters : ${characters}\n` : "") +
      (groups ? `Groups : ${groups}\n` : "") +
      (book.pages ? `Pages : ${book.pages.length}\n` : "") +
      (book.uploaded
        ? `Uploaded : ${formatUploadDate(book.uploaded)}\n` +
          `${formatDuration(book.uploaded)}\n`
        : "") +
      (book.favorites ? `Favorites : ${book.favorites}\n` : "") +
      `ID : ${book.id}\n\n` +
      `Link : https://nhentai.net/g/${book.id}`;

        // Get cover URL using the new API - no workaround needed!
        // The new package handles multiple hosts and WebP format automatically
        let coverURL = api.getImageURL(book.pages[0]);
        
        // Keep the to-jpg proxy only for LINE compatibility (LINE doesn't support WebP)
        if (coverURL.includes('.webp')) {
          coverURL = `https://to-jpg.vercel.app/convert?url=${encodeURIComponent(coverURL)}&format=jpg`;
        }

    
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
         type: "image",
         originalContentUrl: coverURL,
         previewImageUrl: coverURL,
        },
        {
          type: "text",
          text: replyText,
          
        },
      ],
    });

  }
}