const { QuickDB } = require("quick.db");
const fs = require('fs');
const path = require('path');
const db = new QuickDB();

const wordlistPath = path.join(__dirname, '../../utils/indo-wordlist.txt');
let wordlist = [];
try {
  wordlist = fs.readFileSync(wordlistPath, 'utf8')
    .split('\n')
    .map(w => w.trim().toLowerCase())
    .filter(w => w.length > 0);
} catch (error) {
  console.error("Fatal Error: Could not load Indonesian wordlist:", error);
  wordlist = [];
}

function getRandomWord() {
  if (wordlist.length === 0) return null;
  return wordlist[Math.floor(Math.random() * wordlist.length)];
}

function getRandomSubstring(word) {
  if (!word || word.length <= 3) return word;
  const maxLength = word.length;
  let start;
  if (maxLength <= 3) {
      start = 0;
  } else {
      start = Math.floor(Math.random() * (maxLength - 2));
  }
  return word.substring(start, Math.min(start + 3, maxLength));
}

// Now includes player name and uses English, no markdown
function generatePrompt(gameState) {
    if (!gameState || !gameState.currentSubstring || !gameState.playerName) {
        return "Error: Could not get game data."; // English
    }
    const healthDisplay = gameState.health > 0 ? '💖'.repeat(gameState.health) : '💔';
    // English text, player name, no markdown
    return `❓ Word Guess ❓\n\n${gameState.playerName}, type a valid Indonesian word containing: ${gameState.currentSubstring}\n\nHealth: ${healthDisplay}\nScore: ${gameState.score}\n(Type 'Stop' to quit)`;
}

module.exports = {
  command: "wordguess",
  aliases: ["wg"],
  category: "game",
  description: "Guess an Indonesian word based on a random substring!", // English
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const userId = event.source.userId;
    const groupId = event.source.groupId || event.source.roomId || userId;

    if (wordlist.length === 0) {
        return client.replyMessage({
            replyToken: event.replyToken,
            // English
            messages: [{ type: "text", text: "Sorry, the word list could not be loaded. Cannot start the game." }],
        });
    }

    const isGameActive = await db.get(`${groupId}_wordguessActive`);

    if (isGameActive) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          // English
          text: "A Word Guess game is already active here. Finish it first or type 'Stop'.",
          quoteToken: event.message.quoteToken,
        }],
      });
    }

    // Get player profile
    let playerName = "Player";
    try {
        const profile = await client.getGroupMemberProfile(groupId, userId);
        playerName = profile.displayName;
    } catch (profileError) {
        console.error(`Failed to get profile for ${userId}:`, profileError);
    }

    const initialHealth = Math.min(parseInt(args[0]) || 3, 7);
    const initialWord = getRandomWord();
    const initialSubstring = getRandomSubstring(initialWord);

    if (!initialSubstring) {
         return client.replyMessage({
            replyToken: event.replyToken,
            // English
            messages: [{ type: "text", text: "Sorry, failed to generate the first word snippet. Please try again." }],
        });
    }

    const initialState = {
      score: 0,
      health: initialHealth,
      playerName: playerName, // Store player name
      usedWords: [],
      currentSubstring: initialSubstring,
      stopped: false,
    };

    await db.set(`${groupId}_wordguessActive`, true);
    await db.set(`${groupId}_wordguessOwner`, userId);
    await db.set(`${groupId}_wordguessState`, initialState);

    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "text",
          text: generatePrompt(initialState), // Pass initial state with name
        }
      ]
    });
  },
  wordlist,
  getRandomWord,
  getRandomSubstring,
  generatePrompt
};