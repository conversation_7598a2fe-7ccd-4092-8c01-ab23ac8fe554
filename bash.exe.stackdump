Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x2118E
0007FFFF9DC0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x69BA
0007FFFF9DC0  0002100469F2 (00021028DF99, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DC0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DC0  00021006A545 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA0A0  00021006B9A5 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAE2680000 ntdll.dll
7FFAE1D80000 KERNEL32.DLL
7FFADFE60000 KERNELBASE.dll
7FFAE05A0000 USER32.dll
7FFADFCC0000 win32u.dll
7FFAE20E0000 GDI32.dll
7FFADFA00000 gdi32full.dll
7FFADFDB0000 msvcp_win.dll
7FFADF810000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAE0770000 advapi32.dll
7FFAE04F0000 msvcrt.dll
7FFAE0440000 sechost.dll
7FFAE1BE0000 RPCRT4.dll
7FFADEE00000 CRYPTBASE.DLL
7FFADF960000 bcryptPrimitives.dll
7FFAE1EC0000 IMM32.DLL
