{"name": "v<PERSON><PERSON><PERSON>", "version": "2506-LTS", "description": "A LINE bot with many features", "main": "index.js", "engines": {"node": ">=18.0.0", "vscode": "^1.22.0"}, "bin": {"vajrabot": "index.js"}, "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["LINE", "linebot", "official account", "OA", "bot"], "repository": {"type": "git", "url": "git+https://github.com/Uryaaa/VajraBot.git"}, "bugs": {"url": "https://github.com/Uryaaa/VajraBot/issues"}, "homepage": "https://github.com/Uryaaa/VajraBot", "private": true, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@date-fns/tz": "^1.2.0", "@line/bot-sdk": "^9.7.1", "@ngrok/ngrok": "^1.4.1", "aki-api": "^7.0.1", "archiver": "^7.0.1", "axios": "^1.7.9", "better-sqlite3": "^11.9.0", "cheerio": "^1.0.0", "cli-table3": "^0.6.5", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "hanime": "^1.2.1", "he": "^1.2.0", "http-cookie-agent": "^6.0.8", "mathjs": "^13.2.3", "nhentai-api": "github:Uryaaa/nhentai-api", "puppeteer": "^23.11.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "quick.db": "^9.1.7", "tough-cookie": "^5.1.0", "wanakana": "^5.3.1", "winston": "^3.17.0", "youtube-sr": "^4.3.11", "yt-dlp-wrap": "^2.3.12"}, "devDependencies": {"pm2": "^6.0.5"}}