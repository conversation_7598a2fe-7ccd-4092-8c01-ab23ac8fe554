const { format, formatDistanceToNow } = require('date-fns');
const { id } = require('date-fns/locale');
const { CookieJar } = require('tough-cookie');
const _httpCookie = require('http-cookie-agent/http');
const { HttpsCookieAgent: CookieAgent } = _httpCookie

module.exports = {
  command: "nhrandom",
  aliases: ["nhr"],
  category: "anime",
  description: "Gacha doujin nhentai",
  requiresPrefix: true, 
  includes: false, 
  handler: async (client, blobClient, event, args) => {
    const { API, TagTypes } = require("nhentai-api");
    // const jar = new CookieJar();
    // const agent = new CookieAgent({ cookies: { jar } });

    // // Split cookies if multiple are present
    // const cookies = process.env.cookies.split(';').map(c => c.trim());
    
    // // Set all cookies asynchronously
    // await Promise.all(cookies.map(cookie => 
    //     jar.setCookie(cookie, 'https://nhentai.net/')
    // ));

     // Formatting functions
    const formatUploadDate = (date) => 
      format(date, "EEEE do MMMM yyyy", { locale: id });
    
    const formatDuration = (date) => 
      formatDistanceToNow(date, {
        addSuffix: false,
        locale: id,
        includeSeconds: false
      });

    const api = new API();
    const randomBook = await api.getRandomBook();
    const tags = randomBook.getTagsWith({ type: TagTypes.Tag }).join(", ");
    const artists = randomBook
      .getTagsWith({ type: TagTypes.Artist })
      .join(", ");
    const languages = randomBook
      .getTagsWith({ type: TagTypes.Language })
      .join(", ");
    const categories = randomBook
      .getTagsWith({ type: TagTypes.Category })
      .join(", ");
    const parodies = randomBook
      .getTagsWith({ type: TagTypes.Parody })
      .join(", ");
    const characters = randomBook
      .getTagsWith({ type: TagTypes.Character })
      .join(", ");
    const groups = randomBook.getTagsWith({ type: TagTypes.Group }).join(", ");

    const replyText =
      (randomBook.title.pretty ? `Title : ${randomBook.title.pretty}\n` : "") +
      (randomBook.title.english
        ? `English Title : ${randomBook.title.english}\n`
        : "") +
      (randomBook.title.japanese
        ? `Japanese Title : ${randomBook.title.japanese}\n`
        : "") +
      (tags ? `\nTags : ${tags}\n\n` : "") +
      (artists ? `Artists : ${artists}\n` : "") +
      (languages ? `Languages : ${languages}\n` : "") +
      (categories ? `Categories : ${categories}\n` : "") +
      (parodies ? `Parodies : ${parodies}\n` : "") +
      (characters ? `Characters : ${characters}\n` : "") +
      (groups ? `Groups : ${groups}\n` : "") +
      (randomBook.pages ? `Pages : ${randomBook.pages.length}\n` : "") +
      (randomBook.uploaded
        ? `Uploaded : ${formatUploadDate(randomBook.uploaded)}\n` +
          `${formatDuration(randomBook.uploaded)}\n`
        : "") +
      (randomBook.favorites ? `Favorites : ${randomBook.favorites}\n` : "") +
      `ID : ${randomBook.id}\n\n` +
      `Link : https://nhentai.net/g/${randomBook.id}`;
        // Get cover URL using the new API - no workaround needed!
        // The new package handles multiple hosts and WebP format automatically
        let coverURL = api.getImageURL(randomBook.pages[0]);
        
        // Keep the to-jpg proxy only for LINE compatibility (LINE doesn't support WebP)
        if (coverURL.includes('.webp')) {
          coverURL = `https://to-jpg.vercel.app/convert?url=${encodeURIComponent(coverURL)}&format=jpg`;
        }

      
      // Cek jika URL dengan t.nhentai.net tidak ditemukan
    
    client.replyMessage({
      replyToken: event.replyToken,
      messages: [
        {
          type: "image",
          originalContentUrl: coverURL ,
          previewImageUrl: coverURL,
        },
        {
          type: "text",
          text: replyText,
          quoteToken: event.message.quoteToken,
        },
        {
          type: "flex",
          altText: "Download doujin",
          contents: {
            type: "bubble",
            size: "micro",
            body: {
              type: "box",
              layout: "vertical",
              spacing: "sm",
              contents: [
                {
                  type: "button",
                  style: "primary",
                  height: "sm",
                  adjustMode: "shrink-to-fit",
                  action: {
                    type: "uri",
                    label: "Download",
                    uri: `https://tamauniverse.vercel.app/api/nhentai/${randomBook.id}/pdf`,
                  },
                },
                {
                  type: "button",
                  style: "primary",
                  height: "sm",
                  adjustMode: "shrink-to-fit",
                  action: {
                    type: "uri",
                    label: "Mirror Download",
                    uri: `https://associated-roberta-tama-universe-5876c21c.koyeb.app/api/nhentai/${randomBook.id}/pdf`,
                  },
                },
              ],
            },
          },
        },
      ],
    });
  }
}