const YTDlpWrap = require("yt-dlp-wrap").default;
const ytDlpWrap = new YTDlpWrap("./bin/yt-dlp.exe");
const fs = require("fs");
const path = require("path");
const ffmpeg = require("fluent-ffmpeg");
const { applyAudioFilters, audioFilterMap } = require("../../utils/filters");
const { formatDuration } = require("../../utils/utils");
const { generateSecureUrl } = require("../../utils/urlSigner");

module.exports = {
  command: "mp3",
  aliases: ["yta"],
  category: "utility",
  description: "Download audio from links with optional filters",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const input = event.message.text.split(" ");
    const videoUrl = input[1];
    const filterInput = input.slice(2).join(" ");

    const maxFileSize = 200 * 1024 * 1024; // 200MB
    const supportedFilters = audioFilterMap ? Object.keys(audioFilterMap) : [];

    if (!videoUrl || !videoUrl.startsWith("https://")) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Invalid or missing URL.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    // **Parsing Filters**
    let filters = [];
    let customFilters = [];

    if (filterInput) {
      const matches = filterInput.match(/\[([^\]]+)\]/); // Extract text within []
      if (matches) {
        const rawFilters = matches[1].split(","); // Split by comma
        rawFilters.forEach((filter) => {
          if (filter.startsWith("custom=")) {
            customFilters.push(filter.replace("custom=", "")); // Store custom filters
          } else if (supportedFilters.includes(filter)) {
            filters.push(filter); // Store preset filters
          }
        });
      }
    }

    let metadata;
    try {
      metadata = await ytDlpWrap.getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Cannot download live videos.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "This video is private.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      }
    } catch (error) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to fetch video metadata.",
            quoteToken: event.message.quoteToken,
          },
        ],
      });
    }

    const user =
      event.source.type === "group"
        ? await client.getGroupMemberProfile(event.source.groupId, event.source.userId)
        : await client.getProfile(event.source.userId);

    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    const timestamp = Date.now();
    const originalFilePath = path.join(downloadsDir, `${timestamp}_original.mp3`);
    const filteredFilePath = path.join(downloadsDir, `${timestamp}_filtered.mp3`);

    ytDlpWrap
      .exec([videoUrl, "-x", "--audio-format", "mp3", "-o", originalFilePath])
      .on("ytDlpEvent", (eventType, eventData) => console.log(eventType, eventData))
      .on("close", async () => {
        let finalFilePath = originalFilePath;
        let duration = metadata.duration * 1000;

        if (filters.length > 0 || customFilters.length > 0) {
          try {
            await applyAudioFilters(originalFilePath, filteredFilePath, [...filters, ...customFilters]);

            // Verify the filtered file was created and is valid
            if (!fs.existsSync(filteredFilePath) || fs.statSync(filteredFilePath).size === 0) {
              throw new Error("Filtered audio file is empty or not created");
            }

            finalFilePath = filteredFilePath;
            duration = await new Promise((resolve, reject) => {
              ffmpeg.ffprobe(filteredFilePath, (err, data) =>
                err ? reject(err) : resolve(data.format.duration * 1000)
              );
            });
          } catch (error) {
            console.error("Audio filter error:", error);

            // Clean up failed filtered file if it exists
            if (fs.existsSync(filteredFilePath)) {
              fs.unlinkSync(filteredFilePath);
            }

            return client.replyMessage({
              replyToken: event.replyToken,
              messages: [
                {
                  type: "text",
                  text: `❌ Failed to apply audio filters: ${[...filters, ...customFilters].join(", ")}\n\nThe filters may not be compatible with this audio format. Try using simpler filters like: bassboost, reverb, echo, or tempo.`,
                  quoteToken: event.message.quoteToken,
                },
              ],
            });
          }
        }

        if (fs.statSync(finalFilePath).size > maxFileSize) {
          fs.unlinkSync(finalFilePath);
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "File size exceeds 200MB.",
                quoteToken: event.message.quoteToken,
              },
            ],
          });
        }

        // Generate secure URL for the audio file
        const audioUrl = generateSecureUrl(`/downloads/${path.basename(finalFilePath)}`, 120); // 2 hours expiry

        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "audio",
              originalContentUrl: audioUrl, // Use secure URL
              duration,
            },
            {
              type: "text",
              text: `🔉 ${metadata.title}\nFilters: ${
                [...filters, ...customFilters].join(", ") || "Normal"
              }\n\nRequested by ${user.displayName}`,
              quoteToken: event.message.quoteToken,
            },
          ],
        });

        if (filters.length > 0 || customFilters.length > 0) {
          fs.unlinkSync(originalFilePath);
        }
      })
      .on("error", () => {
        client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "Failed to download audio.",
              quoteToken: event.message.quoteToken,
            },
          ],
        });
      });
  },
};
