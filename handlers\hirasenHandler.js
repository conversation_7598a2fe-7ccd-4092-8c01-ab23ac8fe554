const { QuickDB } = require("quick.db");
const db = new QuickDB();
const { generateQuestionPayload } = require('../commands/db/hirasen');

module.exports = async (client, event) => {
  if (event.message.type !== "text") {
    return false;
  }

  const userId = event.source.userId;
  const groupId = event.source.groupId || event.source.roomId || userId;
  const userMessage = event.message.text.trim().toUpperCase();

  const isGameActive = await db.get(`${groupId}_hirasenActive`);
  const gameOwner = await db.get(`${groupId}_hirasenOwner`);

  if (!isGameActive || userId !== gameOwner) {
    return false;
  }

  try {
    let gameState = await db.get(`${groupId}_hirasenState`);
    if (!gameState || gameState.stopped || gameState.health <= 0) {
      await db.delete(`${groupId}_hirasenActive`);
      await db.delete(`${groupId}_hirasenOwner`);
      await db.delete(`${groupId}_hirasenState`);
      return false;
    }

    const validAnswers = ['A', 'B', 'C', 'D'];
    let feedbackText = "";
    let nextQuestionPayload = null;
    let gameContinues = true;

    if (userMessage === "STOP") {
        gameState.stopped = true;
        // English text, no markdown
        feedbackText = `🛑 Game stopped. Final score: ${gameState.score}. Thanks for playing!`;
        gameContinues = false;
        await db.delete(`${groupId}_hirasenActive`);
        await db.delete(`${groupId}_hirasenOwner`);
        await db.delete(`${groupId}_hirasenState`);

    } else if (validAnswers.includes(userMessage)) {
        const expectedAnswer = gameState.correctLetter;
        const expectedTranslation = gameState.correctTranslation;

        if (userMessage === expectedAnswer) {
            gameState.score++;
            // English text, no markdown
            feedbackText = `✅ Correct! The answer was ${expectedAnswer}) ${expectedTranslation}.`;
            nextQuestionPayload = generateQuestionPayload(gameState);
             if (nextQuestionPayload.error) {
                 feedbackText += `\n\n⚠️ Error creating next question: ${nextQuestionPayload.error}. Game stopped.\nFinal score: ${gameState.score}`; // English
                 gameContinues = false;
                 await db.delete(`${groupId}_hirasenActive`);
                 await db.delete(`${groupId}_hirasenOwner`);
                 await db.delete(`${groupId}_hirasenState`);
             } else {
                gameState.correctLetter = nextQuestionPayload.correctLetter;
                gameState.correctTranslation = nextQuestionPayload.correctTranslation;
             }

        } else {
            gameState.health--;
            // English text, no markdown
            feedbackText = `❌ Incorrect! The correct answer was ${expectedAnswer}) ${expectedTranslation}.\nHealth decreased by 1!`;

            if (gameState.health <= 0) {
                gameContinues = false;
                const finalScore = gameState.score;
                await db.delete(`${groupId}_hirasenActive`);
                await db.delete(`${groupId}_hirasenOwner`);
                await db.delete(`${groupId}_hirasenState`);
                // English text, no markdown
                feedbackText += `\n\n💀 Out of health! Game over.\nFinal score: ${finalScore}.`;
            } else {
                 nextQuestionPayload = generateQuestionPayload(gameState);
                 if (nextQuestionPayload.error) {
                     feedbackText += `\n\n⚠️ Error creating next question: ${nextQuestionPayload.error}. Game stopped.\nFinal score: ${gameState.score}`; // English
                     gameContinues = false;
                     await db.delete(`${groupId}_hirasenActive`);
                     await db.delete(`${groupId}_hirasenOwner`);
                     await db.delete(`${groupId}_hirasenState`);
                 } else {
                    gameState.correctLetter = nextQuestionPayload.correctLetter;
                    gameState.correctTranslation = nextQuestionPayload.correctTranslation;
                 }
            }
        }

         if (gameContinues) {
             await db.set(`${groupId}_hirasenState`, gameState);
         }

    } else {
      // Invalid input (not A/B/C/D/STOP) - Silently ignore
      return false; // <<< Let other handlers process it
    }

    let finalMessageText = feedbackText;
    if (gameContinues && nextQuestionPayload && nextQuestionPayload.questionText) {
        finalMessageText += `\n\n${nextQuestionPayload.questionText}`;
    } else if (!gameContinues && feedbackText) {
         finalMessageText = feedbackText;
    }

    if(finalMessageText){
        await client.replyMessage({
            replyToken: event.replyToken,
            messages: [{
                type: "text",
                text: finalMessageText.trim(),
            }],
        });
    }

    return true;

  } catch (error) {
    console.error("Error handling Hirasen message:", error);
    await db.delete(`${groupId}_hirasenActive`);
    await db.delete(`${groupId}_hirasenOwner`);
    await db.delete(`${groupId}_hirasenState`);
    try {
       await client.replyMessage({
          replyToken: event.replyToken,
          // English error
          messages: [{ type: "text", text: `An internal error occurred while playing Hirasen: ${error.message}. The game has been stopped.` }],
        });
    } catch (replyError) {
      console.error("Failed to send error reply:", replyError);
    }
    return true;
  }
};