const { QuickDB } = require("quick.db");
const db = new QuickDB();

module.exports = async (event, client, blobClient) => {
    // Handle unsend events for snipe functionality
    console.log(event.unsend.messageId);

    if (event.source.type === "group" && event.source.groupId) {
        const messageKey = `message_${event.source.groupId}_${event.unsend.messageId}`;
        const messageData = await db.get(messageKey);

        if (messageData) {
            // Mark message as deleted instead of removing it
            messageData.deleted = true;
            messageData.deletedAt = Date.now();
            await db.set(messageKey, messageData);

            console.log(`Message ${event.unsend.messageId} marked as deleted for snipe functionality`);
        }
    }
}
    