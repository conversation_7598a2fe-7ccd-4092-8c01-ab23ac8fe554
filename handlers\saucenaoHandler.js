const { QuickDB } = require("quick.db");
const db = new QuickDB();
const fs = require("fs");
const path = require("path");
const axios = require("axios");
const { generateApiUrl } = require("../utils/urlSigner");

module.exports = async (client, blobClient, event) => {
  const userId = event.source.userId;
  const awaitingImage = await db.get(`saucenao_${userId}`);

  if (!awaitingImage) return false;
  
  if (event.message.type === "image" && event.message.contentProvider.type === "line") {
    let stream = await blobClient.getMessageContent(event.message.id);
    let chunks = [];
    stream.on("data", (chunk) => chunks.push(chunk));

    const timestamp = Date.now();
    stream.on("end", async () => {
      console.log("Stream selesai, mulai menyimpan gambar.");
      const buffer = Buffer.concat(chunks);
      const dirPath = path.join(__dirname, "../static/downloads");
      const filePath = path.join(dirPath, `${timestamp}.jpg`);

      if (!fs.existsSync(dirPath)) fs.mkdirSync(dirPath, { recursive: true });

      fs.writeFileSync(filePath, buffer);
      console.log(`Gambar berhasil disimpan di ${filePath}`);
      await db.delete(`saucenao_${userId}`);

      // Generate API URL for external service access
      const imageUrl = generateApiUrl(`/downloads/${timestamp}.jpg`);

      try {
        const res = await axios.get(
          `https://saucenao.com/search.php?db=999&output_type=2&numres=5&api_key=${process.env.saucenao}&url=${imageUrl}`
        );

        if (!res.data?.results?.length) {
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              { type: "text",
                text: "No similar images found.",
                quoteToken: event.message.quoteToken, 
              }
            ],
          });
        }

        const sauce = res.data.results[0];
        const extUrls = sauce.data.ext_urls || [];
        const getConfidenceText = (percentage) => {
          if (percentage >= 90) return "🔥 Looks like a perfect match!";
          if (percentage >= 80) return "👌 Pretty close match!";
          if (percentage >= 70) return "🤔 Could be right, but double-check!";
          return "😬 Not too sure about this one...";
        };
        const confidenceText = getConfidenceText(parseFloat(sauce.header.similarity));
        // Convert sauce.data keys dynamically
        const formatKey = (key) =>
          key.replace(/_/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());

        let replyText = `${confidenceText}\n\nSimilarity: ${sauce.header.similarity}%\n`;

        for (const [key, value] of Object.entries(sauce.data)) {
          if (value && typeof value !== "object" && key !== "ext_urls") {
            replyText += `${formatKey(key)}: ${value}\n`;
          }
        }

        replyText +=
          "===========================\n" +
          (extUrls.length > 0 ? "External URLs:\n- " + extUrls.join("\n- ") : "");

        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
              type: "text", 
              text: replyText,
              quoteToken: event.message.quoteToken,  
            }
          ],
        });
      } catch (error) {
        console.error("Error in SauceNAO request:", error);
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            { 
              type: "text", 
              text: "An error occurred while processing the image.",
              quoteToken: event.message.quoteToken, 
            }
          ],
        });
      }
    });

    stream.on("error", async (err) => {
      console.error("Error downloading content:", err);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          { 
            type: "text", 
            text: "An error occurred while downloading the image.",
            quoteToken: event.message.quoteToken,  
          }
        ],
      });
    });

    return true;
  }

  return false;
};
