const YTDlpWrap = require("yt-dlp-wrap").default;
const ytDlpWrap = new YTDlpWrap("./bin/yt-dlp.exe");
const fs = require("fs");
const path = require("path");
const { formatDuration } = require("../utils/utils");
const { generateSecureUrl } = require("../utils/urlSigner");
module.exports = {
  postbackData: "audioYoutubeData=", // Prefix for dynamic sticker postbacks
  cooldown: 10,
  handler: async (client, blobClient, event, id) => {
     const videoUrl = id // Use dynamic URL from args if provided 
     const maxFileSize = 200 * 1024 * 1024;
    // Fetch metadata for the video
    let metadata;
    try {
      metadata = await ytDlpWrap.getVideoInfo(videoUrl);

      if (metadata.is_live) {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            
            { type: "text", 
              text: "You cannot download live videos." 
            },
          ],
        });
      }

      if (metadata.availability === "private") {
        return client.replyMessage({
          replyToken: event.replyToken,
          messages: [
            {
              type: "text",
              text: "This video is private and cannot be downloaded.",
            },
          ],
        });
      }

    } catch (error) {
      console.error("Failed to fetch video metadata:", error);
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: "text",
            text: "Failed to fetch video metadata. Please try again later.",
          },
        ],
      });
    }
let user;

if (event.source.type === "group") {
  // If event is from a group, use getGroupMemberProfile
  user = await client.getGroupMemberProfile(
    event.source.groupId,
    event.source.userId
  );
} else {
  // Otherwise, use the standard getProfile for individual chat
  user = await client.getProfile(event.source.userId);
}

// Send initial confirmation message to the user
let userProfile = user.displayName;
    // client.replyMessage(event.replyToken, {
    //   type: "text",
    //   text: `Downloading ${user} requested audio...`,
      
    // });

    // Ensure the ./downloads directory exists
    const downloadsDir = "./static/downloads";
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir);
    }

    // Generate a timestamped filename
    const timestamp = Date.now();
    const outputFilePath = path.join(downloadsDir, `${timestamp}.mp3`);

   ytDlpWrap
  .exec([
    videoUrl, // Gunakan URL video yang disediakan
    "-x", // Ekstrak audio
    "--audio-format", "mp3", // Format audio
    "-o", outputFilePath // Simpan file audio di path yang diinginkan
  ])
      .on("ytDlpEvent", (eventType, eventData) =>
        console.log(eventType, eventData)
      )
      .on("error", (error) => console.error(error))
      .on("close", () => {
        // Determine the preview image URL
        if(fs.statSync(outputFilePath).size > maxFileSize) {
          fs.unlink(outputFilePath, (err) => {
            if (err) {
              console.error("Error deleting file:", err);
            } else {
              console.log("File deleted successfully");
            }
          });
          return client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: "text",
                text: "Video size exceeds the maximum allowed size of 200 MB.",
                
              },
            ],
          });
        }

        // Generate secure URL for the audio file
        const audioUrl = generateSecureUrl(`/downloads/${timestamp}.mp3`, 120); // 2 hours expiry

        // Send the downloaded audio to the user
        const messageContent = [
        {
          type: "audio",
          originalContentUrl: audioUrl, // Use secure URL
          duration: Number(metadata.duration *1000),
        },
        {
          type: "text",
          text: `🔉${metadata.title}\n${formatDuration(metadata.duration)}\n\nRequested by ${userProfile}`,
        }
      ]
        client.replyMessage({
          replyToken: event.replyToken,
          messages: messageContent,
        });
        // if (event.source.type === "group") {
        //   client.pushMessage(event.source.groupId, messageContent);
        // } else {
        //   client.pushMessage(event.source.userId, messageContent);
        // }
        // Use pushMessage to send the video
      });
  },
};
