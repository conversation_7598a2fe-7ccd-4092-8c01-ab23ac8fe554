const { QuickDB } = require("quick.db");
const fetch = require('node-fetch');
const he = require('he');
const db = new QuickDB();

const DIFFICULTIES = ['easy', 'medium', 'hard'];
const LETTERS = ['A', 'B', 'C', 'D'];

function shuffleArray(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

async function fetchTriviaQuestion(difficulty) {
    const url = `https://opentdb.com/api.php?amount=1&type=multiple${difficulty ? `&difficulty=${difficulty}` : ''}`;
    try {
        const res = await fetch(url);
        if (!res.ok) {
             let errorBody = '';
             try { errorBody = await res.text(); } catch(e){}
             console.error(`API Error Response: ${res.status} ${res.statusText}`, errorBody);
             throw new Error(`Failed to fetch data from API (Status: ${res.status})`);
        }
        const data = await res.json();
        if (data.response_code !== 0 || !data.results || data.results.length === 0) {
             let errMsg = "API returned an empty or invalid response.";
             if(data.response_code === 1) errMsg = "Not enough questions in the database for your query.";
             else if (data.response_code === 2) errMsg = "Invalid parameter in the API request.";
             console.warn(`OpenTDB API Response Code: ${data.response_code}`);
             throw new Error(errMsg);
        }
        return data.results[0];
    } catch (error) {
        console.error("Error fetching trivia:", error);
        return { error: error.message || "An error occurred while contacting the Trivia API." };
    }
}

function formatQuestionPayload(triviaData, gameState) {
    if (!triviaData || typeof triviaData !== 'object' || triviaData.error) {
        return { error: triviaData?.error || "Failed to process trivia data." };
    }
    if (!gameState || !gameState.playerName) {
         return { error: "Player data is missing for formatting."};
    }

    const question = he.decode(triviaData.question);
    const correctAnswer = he.decode(triviaData.correct_answer);
    const incorrectAnswers = triviaData.incorrect_answers.map(he.decode);
    const allAnswers = shuffleArray([correctAnswer, ...incorrectAnswers]);
    const correctIndex = allAnswers.indexOf(correctAnswer);
    const correctLetter = LETTERS[correctIndex];

    const healthDisplay = gameState.health > 0 ? '💖'.repeat(gameState.health) : '💔';
    const difficultyDisplay = he.decode(triviaData.difficulty);

    // Removed markdown from options and instructions
    const questionText = `🧠 Trivia Quiz (${difficultyDisplay}) 🧠\n\n${gameState.playerName}, ${question}\n\nOptions:\n${allAnswers.map((opt, i) => `${LETTERS[i]}) ${opt}`).join('\n')}\n\nType your answer (A/B/C/D) or 'Stop'.\n\nHealth: ${healthDisplay}\nScore: ${gameState.score}`;

    return {
        questionText,
        correctLetter,
        correctAnswerText: correctAnswer,
    };
}

module.exports = {
  command: "trivia",
  aliases: ["quiz"],
  category: "game",
  description: "Play a trivia quiz!",
  requiresPrefix: true,
  includes: false,
  handler: async (client, blobClient, event, args) => {
    const userId = event.source.userId;
    const groupId = event.source.groupId || event.source.roomId || userId;

    const isGameActive = await db.get(`${groupId}_triviaActive`);
    if (isGameActive) {
      return client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
          type: "text",
          text: "A trivia quiz is already active here. Please wait or type 'Stop'.",
          quoteToken: event.message.quoteToken,
        }],
      });
    }

    const difficultyArg = args[0]?.toLowerCase();
    const difficulty = DIFFICULTIES.includes(difficultyArg) ? difficultyArg : undefined;

    let playerName = "Player";
    try {
        const profile = await client.getGroupMemberProfile(groupId, userId);
        playerName = profile.displayName;
    } catch (profileError) {
        console.error(`Failed to get profile for ${userId}:`, profileError);
    }

    const triviaData = await fetchTriviaQuestion(difficulty);

    if (!triviaData || triviaData.error) {
        const errorMessage = triviaData?.error || "Sorry, failed to fetch a trivia question right now. Please try again later.";
        return client.replyMessage({
            replyToken: event.replyToken,
            messages: [{ type: "text", text: errorMessage }],
        });
    }

    const initialState = {
      score: 0,
      health: 3,
      difficulty: difficulty,
      playerName: playerName,
      correctLetter: null,
      correctAnswerText: null,
      stopped: false,
    };

    const firstQuestionPayload = formatQuestionPayload(triviaData, initialState);

     if (firstQuestionPayload.error) {
        return client.replyMessage({
            replyToken: event.replyToken,
            messages: [{ type: "text", text: `Error formatting question: ${firstQuestionPayload.error}` }],
        });
    }

    initialState.correctLetter = firstQuestionPayload.correctLetter;
    initialState.correctAnswerText = firstQuestionPayload.correctAnswerText;

    await db.set(`${groupId}_triviaActive`, true);
    await db.set(`${groupId}_triviaOwner`, userId);
    await db.set(`${groupId}_triviaState`, initialState);

    await client.replyMessage({
        replyToken: event.replyToken,
        messages: [{
            type: "text",
            text: firstQuestionPayload.questionText,
        }],
    });
  },
  fetchTriviaQuestion,
  formatQuestionPayload,
  LETTERS,
};